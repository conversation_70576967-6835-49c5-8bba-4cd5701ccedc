// <PERSON><PERSON><PERSON><PERSON> drawing
// This file contains <PERSON><PERSON><PERSON><PERSON> retracement drawing and completion logic

const endFib = () => {
  if (!fibActive) return;
  fibActive = false;

  // finalize P2
  if (!lastPoint || typeof lastPoint.y !== 'number') {
    // cleanup temps
    if (tempStart) { try { series.removePriceLine(tempStart); } catch(e){} tempStart=null; }
    if (tempEnd)   { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd=null; }
    return;
  }
  const fibEndPrice = snap(series.coordinateToPrice(lastPoint.y));
  const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

  // remove temps
  if (tempStart) { try { series.removePriceLine(tempStart); } catch(e){} tempStart=null; }
  if (tempEnd)   { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd=null; }

  // Get end time for time-bounded Fib levels
  const fibEndTime = chart.timeScale().coordinateToTime(lastPoint.x);

  // create time-bounded permanent lines
  const p1 = createFibLevel(fibStartPrice, fibStartTime, fibEndTime, { color:'#f0ad4e', lineWidth:2, kind:'fib1', group, title:'P1' });
  const p2 = createFibLevel(fibEndPrice, fibStartTime, fibEndTime, { color:'#f0ad4e', lineWidth:2, kind:'fib2', group, title:'P2' });

  // Calculate Fibonacci retracement levels (from high back toward low)
  const range = fibEndPrice - fibStartPrice;
  const fib50 = snap(fibStartPrice + range * 0.5);
  const fib618 = snap(fibEndPrice - range * 0.618); // 61.8% retracement from high
  const fib786 = snap(fibEndPrice - range * 0.786); // 78.6% retracement from high

  // Calculate take profit levels (extensions beyond P2)
  const fibtp236 = snap(fibEndPrice + range * 0.236);
  const fibtp382 = snap(fibEndPrice + range * 0.382); // Changed from 61.8% to 38.2%

  // Create time-bounded retracement lines
  const p50 = createFibLevel(fib50, fibStartTime, fibEndTime, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib50', group, title:'50%' });
  const p618 = createFibLevel(fib618, fibStartTime, fibEndTime, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib618', group, title:'61.8%' });
  const p786 = createFibLevel(fib786, fibStartTime, fibEndTime, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib786', group, title:'78.6%' });

  // Create time-bounded take profit lines
  const ptp236 = createFibLevel(fibtp236, fibStartTime, fibEndTime, { color:'#00ff88', lineWidth:2, lineStyle:1, kind:'fibtp236', group, title:'TP -23.6%' });
  const ptp382 = createFibLevel(fibtp382, fibStartTime, fibEndTime, { color:'#00ff88', lineWidth:2, lineStyle:1, kind:'fibtp382', group, title:'TP -38.2%' });

  // Create a single list entry for the entire Fibonacci set
  const fibSetId = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
  const fibSetRec = { id: fibSetId, p1: fibStartPrice, p2: fibEndPrice, kind: 'fibset', group, handle: null };
  lines.push(fibSetRec);
  addListItem(fibSetRec);

  console.log('Fib set: P1=', fibStartPrice, ' P2=', fibEndPrice, ' levels=', { fib50, fib618, fib786, fibtp236, fibtp382 });
  post('lw_fib_added', { p1:fibStartPrice, p2:fibEndPrice, fib50, fib618, fib786, fibtp236, fibtp382 });

  // Dispatch custom event to parent (Dash) - make it bubble and target parent.document
  const fibCompletedEvent = new CustomEvent('fib_completed', {
    detail: {
      type: 'manual-fib',
      id: fibSetId,
      p1: fibStartPrice,
      p2: fibEndPrice,
      fib50: fib50,
      fib618: fib618,
      fib786: fib786,
      fibtp236: fibtp236,
      fibtp382: fibtp382,
      symbol: window.currentSymbol || currentSymbol || 'UNKNOWN',
      exchange: window.currentExchange || currentExchange || 'UNKNOWN',
      timestamp: Date.now()
    },
    bubbles: true
  });

  // Prefer dispatching on the parent's document so Dash's EventListener receives it
  try {
    if (window.parent && window.parent !== window && window.parent.document) {
      window.parent.document.dispatchEvent(fibCompletedEvent);
    } else {
      document.dispatchEvent(fibCompletedEvent);
    }
  } catch (e) {
    // Fallbacks
    try { window.parent?.dispatchEvent(fibCompletedEvent); } catch(_) {}
    try { window.dispatchEvent(fibCompletedEvent); } catch(_) {}
  }

  console.log('🎯 Fib completion event dispatched to Dash');

  fibStartPrice = null;
  fibStartTime = null;

  // Automatically switch back to normal mode after completing Fibonacci retracement
  setMode('normal');
};

